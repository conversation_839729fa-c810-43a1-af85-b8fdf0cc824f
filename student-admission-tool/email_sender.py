#!/usr/bin/env python3
"""
SMTP SSL 邮件发送工具
Author: Kerwin
Date: 2025-07-18

功能：使用SMTP SSL协议发送邮件
支持：HTML格式邮件、附件、批量发送
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from email.utils import formataddr
from email.header import Header
from pathlib import Path
import os
from typing import List, Optional
import logging


class EmailSender:
    """
    SMTP SSL 邮件发送器
    """

    def __init__(self, smtp_server: str, smtp_port: int, username: str, password: str, sender_name: str = None):
        """
        初始化邮件发送器

        @param smtp_server: SMTP服务器地址
        @param smtp_port: SMTP端口号（SSL通常为465）
        @param username: 邮箱用户名
        @param password: 邮箱密码或应用密码
        @param sender_name: 发件人显示名称（可选）
        """
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.sender_name = sender_name

        # 配置日志
        logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
        self.logger = logging.getLogger(__name__)

    def sendEmail(
        self,
        to_emails: List[str],
        subject: str,
        body: str,
        is_html: bool = False,
        cc_emails: Optional[List[str]] = None,
        bcc_emails: Optional[List[str]] = None,
        attachments: Optional[List[str]] = None,
    ) -> bool:
        """
        发送邮件

        @param to_emails: 收件人邮箱列表
        @param subject: 邮件主题
        @param body: 邮件正文
        @param is_html: 是否为HTML格式
        @param cc_emails: 抄送邮箱列表
        @param bcc_emails: 密送邮箱列表
        @param attachments: 附件文件路径列表
        @return: 发送是否成功
        """
        try:
            # -------------- 1、创建邮件对象 ------------------------
            message = MIMEMultipart()

            # 设置发件人（包含名称和邮箱）
            if self.sender_name:
                message["From"] = formataddr((self.sender_name, self.username))
            else:
                message["From"] = self.username

            message["To"] = ", ".join(to_emails)
            message["Subject"] = subject

            if cc_emails:
                message["Cc"] = ", ".join(cc_emails)

            # -------------- 2、添加邮件正文 ------------------------
            if is_html:
                message.attach(MIMEText(body, "html", "utf-8"))
            else:
                message.attach(MIMEText(body, "plain", "utf-8"))

            # -------------- 3、添加附件 ----------------------------
            if attachments:
                self._addAttachments(message, attachments)

            # -------------- 4、建立SSL连接并发送邮件 ----------------
            all_recipients = to_emails.copy()
            if cc_emails:
                all_recipients.extend(cc_emails)
            if bcc_emails:
                all_recipients.extend(bcc_emails)

            context = ssl.create_default_context()

            with smtplib.SMTP_SSL(self.smtp_server, self.smtp_port, context=context) as server:
                server.login(self.username, self.password)
                text = message.as_string()
                server.sendmail(self.username, all_recipients, text)

            self.logger.info(f"邮件发送成功: {subject} -> {to_emails}")
            return True

        except Exception as e:
            self.logger.error(f"邮件发送失败: {str(e)}")
            return False

    def _addAttachments(self, message: MIMEMultipart, attachments: List[str]):
        """
        添加附件到邮件

        @param message: 邮件对象
        @param attachments: 附件文件路径列表
        """
        import mimetypes
        from email.header import Header

        for file_path in attachments:
            if not Path(file_path).exists():
                self.logger.warning(f"附件文件不存在: {file_path}")
                continue

            try:
                filename = Path(file_path).name

                # 根据文件扩展名确定MIME类型
                mime_type, _ = mimetypes.guess_type(file_path)
                if mime_type is None:
                    mime_type = "application/octet-stream"

                main_type, sub_type = mime_type.split("/", 1)

                with open(file_path, "rb") as attachment_file:
                    part = MIMEBase(main_type, sub_type)
                    part.set_payload(attachment_file.read())

                encoders.encode_base64(part)

                # 处理中文文件名编码
                try:
                    # 尝试ASCII编码
                    filename.encode("ascii")
                    # 如果成功，直接使用
                    part.add_header("Content-Disposition", "attachment", filename=filename)
                except UnicodeEncodeError:
                    # 包含非ASCII字符，使用RFC 2231编码
                    import urllib.parse

                    encoded_filename = urllib.parse.quote(filename)
                    part.add_header("Content-Disposition", f"attachment; filename*=UTF-8''{encoded_filename}")

                message.attach(part)
                self.logger.info(f"已添加附件: {filename}")

            except Exception as e:
                self.logger.error(f"添加附件失败 {file_path}: {str(e)}")

    def sendBatchEmails(self, email_list: List[dict], common_subject: str = None, common_body: str = None) -> dict:
        """
        批量发送邮件

        @param email_list: 邮件信息列表，每个元素包含邮件详情
        @param common_subject: 公共主题（可选）
        @param common_body: 公共正文（可选）
        @return: 发送结果统计
        """
        results = {"success": 0, "failed": 0, "details": []}

        for email_info in email_list:
            subject = email_info.get("subject", common_subject)
            body = email_info.get("body", common_body)
            to_emails = email_info.get("to_emails", [])

            if not subject or not body or not to_emails:
                self.logger.warning("邮件信息不完整，跳过")
                results["failed"] += 1
                continue

            success = self.sendEmail(
                to_emails=to_emails,
                subject=subject,
                body=body,
                is_html=email_info.get("is_html", False),
                cc_emails=email_info.get("cc_emails"),
                bcc_emails=email_info.get("bcc_emails"),
                attachments=email_info.get("attachments"),
            )

            if success:
                results["success"] += 1
            else:
                results["failed"] += 1

            results["details"].append({"to_emails": to_emails, "subject": subject, "success": success})

        return results


def main():
    """
    主函数：邮件发送示例
    """
    print("SMTP SSL 邮件发送工具")
    print("Author: Kerwin | Date: 2025-07-18")
    print("=" * 50)

    # -------------- 1、邮件配置示例 --------------------------
    # 请根据实际情况修改以下配置
    SMTP_SERVER = "smtp.xmut.edu.cn"  # 修改为您的SMTP服务器
    SMTP_PORT = 465
    USERNAME = "<EMAIL>"  # 修改为您的邮箱
    PASSWORD = "2kVWtUnMU3Cagqe9"  # 修改为您的应用密码或授权码

    # -------------- 2、发送邮件示例 --------------------------
    print("邮件发送示例（请先修改配置）：")
    print(f"SMTP服务器: {SMTP_SERVER}:{SMTP_PORT}")
    print(f"发送邮箱: {USERNAME}")
    print()

    # 检查是否为默认配置
    if USERNAME == "<EMAIL>" or PASSWORD == "your_app_password":
        print("⚠️  请先修改脚本中的邮箱配置信息！")
        print("需要配置：")
        print("  - SMTP_SERVER: SMTP服务器地址")
        print("  - SMTP_PORT: SMTP端口号")
        print("  - USERNAME: 您的邮箱地址")
        print("  - PASSWORD: 您的邮箱密码或应用专用密码")
        return

    # 创建邮件发送器
    email_sender = EmailSender(SMTP_SERVER, SMTP_PORT, USERNAME, PASSWORD, "厦门理工学院招生办")

    # -------------- 3、发送测试邮件 --------------------------
    test_emails = ["<EMAIL>"]  # 修改为实际收件人

    # # 发送文本邮件
    # success = email_sender.sendEmail(
    #     to_emails=test_emails,
    #     subject="测试邮件 - 文本格式",
    #     body="这是一封测试邮件。\n\n发送时间：2025-07-18\n发送工具：SMTP SSL 邮件发送器",
    #     is_html=False,
    # )

    # if success:
    #     print("✅ 文本邮件发送成功")
    # else:
    #     print("❌ 文本邮件发送失败")

    # 发送HTML邮件
    html_body = """
    <html>
      <body>
        <p>同学你好！根据录取考核结果，经学校审定，我们很遗憾的通知你未达到厦门理工学院2025年国际学生的录取标准，今年不予录取。感谢你的报考，祝你身体健康，一切顺意！</p>
      </body>
    </html>
    """

    success = email_sender.sendEmail(
        to_emails=test_emails, subject="厦门理工学院2025年国际生招生录取信息", body=html_body, is_html=True
    )

    if success:
        print("✅ HTML邮件发送成功")
    else:
        print("❌ HTML邮件发送失败")


if __name__ == "__main__":
    main()
