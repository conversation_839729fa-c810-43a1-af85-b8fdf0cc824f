# 国际生录取通知邮件发送工具

## 功能概述

自动化邮件发送工具，支持：

- 📧 **不予录取通知** - 向未达到录取标准的考生发送拒绝通知
- 📋 **递补录取通知** - 向递补考生发送包含排序信息的通知邮件
- 🔄 **智能频率控制** - 遵守邮件服务商限制
- 📊 **批量处理** - 支持分批发送

## 快速使用

### 1. 安装依赖

```bash
pip install pandas openpyxl
```

### 2. 配置邮箱

在 `send_admission_notifications.py` 中修改 SMTP 配置（第 420-423 行）：

```python
SMTP_SERVER = "your_smtp_server"
USERNAME = "<EMAIL>"
PASSWORD = "your_password"
```

### 3. 准备 Excel 文件

确保包含以下列：中文名、护照姓名、电子邮件、国籍、专业、录取结论

### 4. 运行程序

```bash
python send_admission_notifications.py
```

## 核心文件

| 文件                              | 功能       |
| --------------------------------- | ---------- |
| `send_admission_notifications.py` | 主程序     |
| `email_sender.py`                 | 邮件发送器 |

## 作者

**Kerwin** - 厦门理工学院
