#!/usr/bin/env python3
"""
测试邮件附件文件名显示
Author: Kerwin
Date: 2025-07-20

功能：发送测试邮件到指定邮箱，验证中文文件名附件是否正确显示
"""

from email_sender import EmailSender
from pathlib import Path


def testAttachmentFilename():
    """
    测试邮件附件文件名显示

    <AUTHOR>
    @Date 2025-07-20
    """
    print("🧪 邮件附件文件名测试")
    print("Author: Ker<PERSON> | Date: 2025-07-20")
    print("=" * 60)

    # -------------- 1、配置邮件发送器 ------------------------
    SMTP_SERVER = "smtp.xmut.edu.cn"
    SMTP_PORT = 465
    USERNAME = "<EMAIL>"
    PASSWORD = "2kVWtUnMU3Cagqe9"

    try:
        email_sender = EmailSender(SMTP_SERVER, SMTP_PORT, USERNAME, PASSWORD, sender_name="厦门理工学院招生处")
        print(f"✅ 邮件发送器初始化成功")
    except Exception as e:
        print(f"❌ 邮件发送器初始化失败: {str(e)}")
        return

    # -------------- 2、检查附件文件 --------------------------
    attachment_path = Path(__file__).parent / "厦门理工学院2025学校奖学金招收国际生招生录取信息确认函.docx"

    if not attachment_path.exists():
        print(f"❌ 附件文件不存在: {attachment_path}")
        return

    print(f"📎 找到附件文件: {attachment_path.name}")
    print(f"📏 文件大小: {attachment_path.stat().st_size / 1024:.1f} KB")

    # -------------- 3、发送测试邮件 --------------------------
    print(f"\n🚀 开始发送测试邮件...")

    # 邮件内容
    subject = "【测试】邮件附件文件名显示测试"
    body = """
    <html>
      <body>
        <h3>邮件附件文件名显示测试</h3>
        <p>此邮件用于测试中文文件名附件的显示效果。</p>
        <p>附件名称应该显示为：<strong>厦门理工学院2025学校奖学金招收国际生招生录取信息确认函.docx</strong></p>
        <p>如果附件名称显示为类似 "tcmime.xxx.bin" 的格式，说明文件名编码有问题。</p>
        <hr>
        <p>测试时间：2025年7月20日</p>
        <p>测试人员：Kerwin</p>
      </body>
    </html>
    """

    # 发送邮件
    success = email_sender.sendEmail(
        to_emails=["<EMAIL>"], subject=subject, body=body, is_html=True, attachments=[str(attachment_path)]
    )

    if success:
        print("✅ 测试邮件发送成功！")
        print(f"   收件人：<EMAIL>")
        print(f"   附件：{attachment_path.name}")
        print(f"   预期显示：厦门理工学院2025学校奖学金招收国际生招生录取信息确认函.docx")
    else:
        print("❌ 测试邮件发送失败")

    print(f"\n📋 测试完成！")
    print(f"💡 提示：")
    print(f"   1. 请检查测试邮箱 <EMAIL>")
    print(f"   2. 验证附件文件名是否正确显示为中文")
    print(f"   3. 如果显示为 'tcmime.xxx.bin'，说明还需要进一步修复")


if __name__ == "__main__":
    testAttachmentFilename()
