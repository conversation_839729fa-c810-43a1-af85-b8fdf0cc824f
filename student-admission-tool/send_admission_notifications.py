#!/usr/bin/env python3
"""
国际生录取通知邮件发送工具
Author: Kerwin
Date: 2025-07-18

功能：
1. 读取Excel文件中的考生信息
2. 发送不予录取通知邮件
3. 发送递补录取通知邮件（包含排序信息）
4. 发送拟录取通知邮件（包含确认函附件）

使用方法：
1. 配置SMTP邮箱信息（第420-423行）
2. 确保Excel文件包含：中文名、护照姓名、电子邮件、国籍、专业、录取结论
3. 确保当前目录下有确认函文件：厦门理工学院2025学校奖学金招收国际生招生录取信息确认函.docx
4. 运行：python send_admission_notifications.py
"""

import pandas as pd
import sys
import re
import random
from pathlib import Path
from email_sender import EmailSender
import logging
from typing import List, Dict, Tuple
import time
from datetime import datetime, timedelta


def queryStudentsByType(excel_file_path: str, notification_type: str) -> List[Dict]:
    """
    根据通知类型查询考生信息

    @param excel_file_path: Excel文件路径
    @param notification_type: 通知类型 ('rejection', 'waitlist' 或 'admission')
    @return: 考生信息列表
    """
    try:
        # -------------- 1、读取Excel文件 ------------------------
        df = pd.read_excel(excel_file_path)

        # -------------- 2、根据类型筛选考生 ----------------------
        if notification_type == "rejection":
            # 筛选不予录取的考生
            filtered_df = df[df["录取结论"].astype(str).str.contains("不予录取", na=False)]
        elif notification_type == "waitlist":
            # 筛选递补录取的考生
            filtered_df = df[df["录取结论"].astype(str).str.contains("递补", na=False)]
        elif notification_type == "admission":
            # 筛选拟录取的考生（只包含录取结论为"录取"的考生）
            filtered_df = df[df["录取结论"].astype(str).str.strip() == "录取"]
        else:
            raise ValueError(f"不支持的通知类型: {notification_type}")

        # -------------- 3、转换为字典列表 ------------------------
        students = []
        for _, row in filtered_df.iterrows():
            student = {
                "chinese_name": row["中文名"] if pd.notna(row["中文名"]) else "未提供",
                "passport_name": row["护照姓名"] if pd.notna(row["护照姓名"]) else "未提供",
                "email": row["电子邮件"] if pd.notna(row["电子邮件"]) else None,
                "nationality": row["国籍"] if pd.notna(row["国籍"]) else "未知",
                "major": row["专业"] if pd.notna(row["专业"]) else "未知",
                "admission_result": row["录取结论"] if pd.notna(row["录取结论"]) else "未知",
            }

            # 对于递补录取考生，提取排序号
            if notification_type == "waitlist":
                waitlist_rank = extractWaitlistRank(student["admission_result"])
                student["waitlist_rank"] = waitlist_rank

            # 只添加有邮箱地址的考生
            if student["email"] and student["email"] != "未提供":
                students.append(student)

        return students

    except Exception as e:
        print(f"读取Excel文件时发生错误: {str(e)}")
        return []


def extractWaitlistRank(admission_result: str) -> int:
    """
    从录取结论中提取递补排序号

    @param admission_result: 录取结论字符串
    @return: 排序号，如果未找到则返回0
    """
    # 使用正则表达式提取数字，支持多种格式
    # 匹配 "递补1", "递补录取3", "递补 5" 等格式
    match = re.search(r"递补(?:录取)?[^\d]*(\d+)", admission_result)
    if match:
        return int(match.group(1))
    return 0


def generateEmailContent(notification_type: str, student_info: Dict = None) -> Dict:
    """
    根据通知类型生成邮件内容

    @param notification_type: 通知类型 ('rejection', 'waitlist' 或 'admission')
    @param student_info: 学生信息（递补通知和拟录取通知时需要）
    @return: 包含主题和正文的字典
    """
    if notification_type == "rejection":
        return generateRejectionContent()
    elif notification_type == "waitlist":
        return generateWaitlistContent(student_info)
    elif notification_type == "admission":
        return generateAdmissionContent(student_info)
    else:
        raise ValueError(f"不支持的通知类型: {notification_type}")


def generateRejectionContent() -> Dict:
    """
    生成不予录取通知邮件内容

    @return: 包含主题和正文的字典
    """
    subject = "厦门理工学院2025年国际生招生录取信息"

    body = """
    <html>
      <body>
        <p>同学你好！根据录取考核结果，经学校审定，我们很遗憾的通知你未达到厦门理工学院2025年国际学生的录取标准，今年不予录取。感谢你的报考，祝你身体健康，一切顺意！</p>
      </body>
    </html>
    """

    return {"subject": subject, "body": body}


def generateWaitlistContent(student_info: Dict) -> Dict:
    """
    生成递补录取通知邮件内容

    @param student_info: 学生信息字典，包含waitlist_rank
    @return: 包含主题和正文的字典
    """
    subject = "厦门理工学院2025年国际生招生录取信息"

    waitlist_rank = student_info.get("waitlist_rank", 0)

    body = f"""
    <html>
      <body>
        <p>同学你好，感谢你的报考申请！根据录取考核结果，经学校审定，你未进入厦门理工学院2025年国际学生正式录取名单，但你被列入后备递补录取名单，递补排序第{waitlist_rank}。当正式录取名单中出现自动放弃录取或9月17日未到校报到者，则我校将按考核成绩高低顺序递补录取后备考生，请予关注。</p>
      </body>
    </html>
    """

    return {"subject": subject, "body": body}


def generateAdmissionContent(student_info: Dict) -> Dict:
    """
    生成拟录取通知邮件内容

    @param student_info: 学生信息字典，包含护照姓名等信息
    @return: 包含主题和正文的字典
    """
    subject = "厦门理工学院2025年国际生招生录取信息"

    # 使用护照姓名作为学生姓名，如果为空则使用中文名，都没有则使用"同学"
    passport_name = student_info.get("passport_name", "").strip()
    chinese_name = student_info.get("chinese_name", "").strip()

    if passport_name:
        student_name = passport_name
    elif chinese_name:
        student_name = chinese_name
    else:
        student_name = "同学"

    body = f"""
    <html>
      <body>
        <p style="text-indent: 0em;">{student_name}同学：</p>
        <p style="text-indent: 2em;">根据个人申请材料和我校综合考核结果，你已被我校列入学校学金招收国际学生拟录取名单。为做好后续录取工作，请明确来校就读意愿并于7月25日12:00前反馈相关信息（见下表）。未予确认者，将视为自动放弃录取资格。</p>
        <p style="text-align: right;">厦门理工学院招生处</p>
        <p style="text-align: right;">2025年7月20日</p>
      </body>
    </html>
    """

    return {"subject": subject, "body": body}


def selectNotificationType() -> str:
    """
    让用户选择要发送的通知类型

    @return: 通知类型 ('rejection', 'waitlist' 或 'admission')
    """
    print(f"\n📧 通知类型选择：")
    print(f"1. 不予录取通知")
    print(f"2. 递补录取通知")
    print(f"3. 拟录取通知")

    while True:
        try:
            choice = input("请选择通知类型 (1/2/3): ").strip()

            if choice == "1":
                return "rejection"
            elif choice == "2":
                return "waitlist"
            elif choice == "3":
                return "admission"
            else:
                print("请输入 1、2 或 3")

        except KeyboardInterrupt:
            print("\n用户取消操作")
            sys.exit(0)


def getSendingMode(total_students: int) -> Dict:
    """
    获取用户选择的发送模式

    @param total_students: 总学生数量
    @return: 包含发送模式配置的字典
    """
    DAILY_LIMIT = 1600

    print(f"\n📋 发送模式选择：")
    print(f"1. 批次模式 - 指定发送范围（适合分多天执行）")
    print(f"2. 全部模式 - 发送所有邮件（当天限制：{DAILY_LIMIT}封）")

    while True:
        try:
            choice = input("请选择发送模式 (1/2): ").strip()

            if choice == "1":
                print(f"\n当前共有 {total_students} 名考生需要发送邮件")
                while True:
                    try:
                        start = int(input(f"请输入开始序号 (1-{total_students}): ")) - 1
                        if start < 0 or start >= total_students:
                            print(f"序号必须在 1-{total_students} 之间")
                            continue

                        max_batch = min(DAILY_LIMIT, total_students - start)
                        size = int(input(f"请输入发送数量 (1-{max_batch}): "))
                        if size < 1 or size > max_batch:
                            print(f"发送数量必须在 1-{max_batch} 之间")
                            continue

                        return {"mode": "batch", "batch_start": start, "batch_size": size}
                    except ValueError:
                        print("请输入有效的数字")

            elif choice == "2":
                if total_students > DAILY_LIMIT:
                    print(f"⚠️  警告：总数量 {total_students} 超过日限制 {DAILY_LIMIT}")
                    confirm = input("是否确认发送（今日最多发送1600封）？(y/N): ").strip().lower()
                    if confirm != "y":
                        continue

                return {"mode": "all", "batch_start": 0, "batch_size": None}

            else:
                print("请输入 1 或 2")

        except KeyboardInterrupt:
            print("\n用户取消操作")
            sys.exit(0)


def sendNotifications(
    students: List[Dict],
    email_sender: EmailSender,
    notification_type: str,
    batch_start: int = 0,
    batch_size: int = None,
) -> Dict:
    """
    发送录取通知邮件，按照频率限制控制发送速度

    邮件发送限制：
    - 每日发送邮件总数上限：2000
    - 每15分钟发出邮件中收信人总人次上限：300
    - 每15分钟发出邮件的总数上限：100
    - 当天发出邮件中收信人总人次上限：2000

    @param students: 考生信息列表
    @param email_sender: 邮件发送器
    @param notification_type: 通知类型 ('rejection' 或 'waitlist')
    @param batch_start: 批次开始索引（从0开始）
    @param batch_size: 批次大小（如果指定，将只发送指定数量的邮件）
    @return: 发送结果统计
    """
    results = {"success": 0, "failed": 0, "total": len(students), "details": []}

    # 频率控制参数
    EMAILS_PER_15MIN = 80  # 每15分钟最多发送80封邮件（保留20%缓冲）
    RECIPIENTS_PER_15MIN = 240  # 每15分钟收件人数上限（保留20%缓冲）
    DAILY_EMAIL_LIMIT = 1600  # 每日邮件总数上限（保留20%缓冲）
    DAILY_RECIPIENT_LIMIT = 1600  # 当日收件人总数上限（保留20%缓冲）

    INTERVAL_MINUTES = 15  # 时间窗口（分钟）
    EMAIL_DELAY_SECONDS = 12  # 每封邮件间隔12秒（每分钟约5封）

    # 确定要发送的学生范围
    if batch_size is not None:
        # 批次模式
        end_index = min(batch_start + batch_size, len(students))
        target_students = students[batch_start:end_index]
        send_count = len(target_students)
        print(f"📦 批次发送模式：第 {batch_start + 1}-{batch_start + send_count} 封邮件")
    else:
        # 全部发送模式
        target_students = students
        send_count = len(target_students)

    # 检查是否超过日限制
    if send_count > DAILY_EMAIL_LIMIT:
        print(f"⚠️  警告：计划发送 {send_count} 封邮件，超过日限制 {DAILY_EMAIL_LIMIT}")
        print(f"建议分批发送，今日最多发送 {DAILY_EMAIL_LIMIT} 封")
        send_count = min(send_count, DAILY_EMAIL_LIMIT)

    print(f"开始发送邮件（{'批次模式' if batch_size else '全部模式'}）...")
    print(f"计划发送 {send_count} 封邮件，共 {len(students)} 名考生")
    print(f"发送策略：每 {EMAIL_DELAY_SECONDS} 秒发送一封，每 {INTERVAL_MINUTES} 分钟最多 {EMAILS_PER_15MIN} 封")
    print("=" * 60)

    # 时间窗口统计
    window_start_time = datetime.now()
    emails_in_window = 0
    recipients_in_window = 0
    daily_emails = 0
    daily_recipients = 0

    for i, student in enumerate(target_students):
        current_time = datetime.now()

        # -------------- 1、检查是否需要等待新的时间窗口 ------------
        time_elapsed = (current_time - window_start_time).total_seconds() / 60

        if time_elapsed >= INTERVAL_MINUTES:
            # 重置时间窗口
            print(f"\n🕐 时间窗口已过 {INTERVAL_MINUTES} 分钟，重置统计")
            print(f"上个窗口发送: {emails_in_window} 封邮件，{recipients_in_window} 个收件人")
            window_start_time = current_time
            emails_in_window = 0
            recipients_in_window = 0

        # -------------- 2、检查时间窗口限制 ----------------------
        if emails_in_window >= EMAILS_PER_15MIN:
            wait_time = INTERVAL_MINUTES * 60 - (current_time - window_start_time).total_seconds()
            if wait_time > 0:
                print(f"\n⏰ 已达到 {INTERVAL_MINUTES} 分钟内邮件数限制，等待 {wait_time:.0f} 秒...")
                time.sleep(wait_time)
                # 重置时间窗口
                window_start_time = datetime.now()
                emails_in_window = 0
                recipients_in_window = 0

        if recipients_in_window >= RECIPIENTS_PER_15MIN:
            wait_time = INTERVAL_MINUTES * 60 - (current_time - window_start_time).total_seconds()
            if wait_time > 0:
                print(f"\n⏰ 已达到 {INTERVAL_MINUTES} 分钟内收件人数限制，等待 {wait_time:.0f} 秒...")
                time.sleep(wait_time)
                # 重置时间窗口
                window_start_time = datetime.now()
                emails_in_window = 0
                recipients_in_window = 0

        # -------------- 3、检查日限制 ----------------------------
        if daily_emails >= DAILY_EMAIL_LIMIT:
            print(f"\n🚫 已达到当日邮件发送上限 {DAILY_EMAIL_LIMIT}，停止发送")
            break

        if daily_recipients >= DAILY_RECIPIENT_LIMIT:
            print(f"\n🚫 已达到当日收件人数上限 {DAILY_RECIPIENT_LIMIT}，停止发送")
            break

        # -------------- 4、发送邮件 ------------------------------
        print(f"\n📧 正在发送第 {i+1}/{send_count} 封邮件")

        if notification_type == "waitlist":
            rank = student.get("waitlist_rank", "未知")
            print(f"   收件人: {student['chinese_name']} ({student['passport_name']}) - 排序第{rank}")
        else:
            print(f"   收件人: {student['chinese_name']} ({student['passport_name']})")

        print(f"   邮箱: {student['email']}")
        print(
            f"   当前窗口: {emails_in_window}/{EMAILS_PER_15MIN} 封邮件，{recipients_in_window}/{RECIPIENTS_PER_15MIN} 个收件人"
        )
        print(
            f"   今日总计: {daily_emails}/{DAILY_EMAIL_LIMIT} 封邮件，{daily_recipients}/{DAILY_RECIPIENT_LIMIT} 个收件人"
        )

        # 生成邮件内容
        email_content = generateEmailContent(notification_type, student)

        # 准备附件（仅拟录取通知需要附件）
        attachments = None
        if notification_type == "admission":
            attachment_path = Path(__file__).parent / "厦门理工学院2025学校奖学金招收国际生招生录取信息确认函.docx"
            if attachment_path.exists():
                attachments = [str(attachment_path)]
                print(f"   📎 包含附件: {attachment_path.name}")
            else:
                print(f"   ⚠️  警告: 确认函附件不存在: {attachment_path}")

        # 发送邮件
        success = email_sender.sendEmail(
            to_emails=[student["email"]],
            subject=email_content["subject"],
            body=email_content["body"],
            is_html=True,
            attachments=attachments,
        )

        # -------------- 5、更新统计信息 --------------------------
        if success:
            results["success"] += 1
            emails_in_window += 1
            recipients_in_window += 1
            daily_emails += 1
            daily_recipients += 1
            print("   ✅ 发送成功")
        else:
            results["failed"] += 1
            print("   ❌ 发送失败")

        results["details"].append({"student": student, "success": success})

        # -------------- 6、发送间隔等待 --------------------------
        if i < send_count - 1:  # 最后一封邮件不需要等待
            print(f"   ⏳ 等待 {EMAIL_DELAY_SECONDS} 秒后发送下一封...")
            time.sleep(EMAIL_DELAY_SECONDS)

    return results


def main():
    """
    主函数：执行录取通知邮件发送
    """
    print("🏫 国际生录取通知邮件发送工具")
    print("Author: Kerwin | Date: 2025-07-18")
    print("=" * 60)

    # -------------- 1、选择通知类型 --------------------------
    notification_type = selectNotificationType()
    type_name_map = {"rejection": "不予录取", "waitlist": "递补录取", "admission": "拟录取"}
    type_name = type_name_map.get(notification_type, "未知")
    print(f"✅ 已选择: {type_name} 通知")

    # -------------- 2、加载考生数据 --------------------------
    excel_file = "【录取15递补5】2025年本科国际生考生录取信息表.xlsx"
    excel_path = Path(__file__).parent.parent / excel_file

    if not excel_path.exists():
        print(f"❌ 错误：找不到Excel文件: {excel_path}")
        sys.exit(1)

    students = queryStudentsByType(excel_path, notification_type)
    print(f"📊 加载到 {len(students)} 名{type_name}且有邮箱的考生")

    if not students:
        print(f"ℹ️  没有找到需要发送{type_name}通知的考生")
        return

    # -------------- 3、显示考生详情 --------------------------
    if notification_type == "waitlist":
        print(f"\n📋 递补考生详情：")
        for i, student in enumerate(students[:10]):  # 只显示前10名
            rank = student.get("waitlist_rank", "未知")
            print(f"   {i+1}. {student['chinese_name']} - 排序第{rank} ({student['email']})")
        if len(students) > 10:
            print(f"   ... 还有 {len(students) - 10} 名考生")
    elif notification_type == "admission":
        print(f"\n📋 拟录取考生详情：")
        for i, student in enumerate(students[:10]):  # 只显示前10名
            print(f"   {i+1}. {student['chinese_name']} - {student['major']} ({student['email']})")
        if len(students) > 10:
            print(f"   ... 还有 {len(students) - 10} 名考生")
        print(f"   📎 附件: 厦门理工学院2025学校奖学金招收国际生招生录取信息确认函.docx")
    else:
        print(f"   前5名考生: {', '.join([s['chinese_name'] for s in students[:5]])}")
        if len(students) > 5:
            print(f"   ... 还有 {len(students) - 5} 名考生")

    # -------------- 4、显示时间预估 --------------------------
    EMAIL_DELAY_SECONDS = 12
    estimated_time_minutes = len(students) * EMAIL_DELAY_SECONDS / 60
    estimated_hours = estimated_time_minutes / 60

    print(f"\n⏱️  时间预估（按每封间隔 {EMAIL_DELAY_SECONDS} 秒计算）：")
    if estimated_hours >= 1:
        print(f"   预计总耗时: {estimated_hours:.1f} 小时 ({estimated_time_minutes:.0f} 分钟)")
    else:
        print(f"   预计总耗时: {estimated_time_minutes:.0f} 分钟")

    # 计算每日发送量和所需天数
    DAILY_LIMIT = 1600
    if len(students) > DAILY_LIMIT:
        days_needed = (len(students) + DAILY_LIMIT - 1) // DAILY_LIMIT  # 向上取整
        print(f"   由于日限制 ({DAILY_LIMIT} 封/天)，需要 {days_needed} 天完成")
        print(f"   建议分批执行：")
        for day in range(days_needed):
            start_idx = day * DAILY_LIMIT
            end_idx = min((day + 1) * DAILY_LIMIT, len(students))
            print(f"     第 {day + 1} 天：发送第 {start_idx + 1}-{end_idx} 封邮件")

    # -------------- 5、选择发送模式 --------------------------
    send_config = getSendingMode(len(students))
    print(f"✅ 已选择: {send_config['mode']} 模式")

    # -------------- 6、配置邮件发送器 ------------------------
    # 请根据实际情况修改以下配置
    SMTP_SERVER = "smtp.xmut.edu.cn"  # 修改为您的SMTP服务器
    SMTP_PORT = 465
    USERNAME = "<EMAIL>"  # 修改为您的邮箱
    PASSWORD = "2kVWtUnMU3Cagqe9"  # 修改为您的应用密码或授权码

    # 检查是否为默认配置
    if USERNAME == "<EMAIL>" or PASSWORD == "your_app_password":
        print("\n⚠️  请先修改脚本中的邮箱配置信息！")
        print("需要配置：")
        print("  - SMTP_SERVER: SMTP服务器地址")
        print("  - SMTP_PORT: SMTP端口号")
        print("  - USERNAME: 您的邮箱地址")
        print("  - PASSWORD: 您的邮箱密码或应用专用密码")
        return

    # -------------- 7、创建邮件发送器 ------------------------
    try:
        email_sender = EmailSender(SMTP_SERVER, SMTP_PORT, USERNAME, PASSWORD, sender_name="厦门理工学院招生处")
        print(f"✅ 邮件发送器初始化成功")
    except Exception as e:
        print(f"❌ 邮件发送器初始化失败: {str(e)}")
        return

    # -------------- 8、发送邮件 ------------------------------
    print(f"\n🚀 开始发送邮件...")

    # 使用用户选择的发送配置
    start_time = datetime.now()

    results = sendNotifications(
        students=students,
        email_sender=email_sender,
        notification_type=notification_type,
        batch_start=send_config["batch_start"],
        batch_size=send_config["batch_size"],
    )

    # -------------- 9、显示发送结果 --------------------------
    end_time = datetime.now()
    actual_duration = end_time - start_time

    print("\n" + "=" * 60)
    print("📈 邮件发送完成！")
    print(f"📊 发送统计：")
    print(f"   总计考生: {results['total']} 名")
    print(f"   发送成功: {results['success']} 封")
    print(f"   发送失败: {results['failed']} 封")

    if len(results["details"]) > 0:
        success_rate = results["success"] / len(results["details"]) * 100
        print(f"   成功率: {success_rate:.1f}%")

    print(f"⏱️  实际耗时: {str(actual_duration).split('.')[0]}")

    if results["failed"] > 0:
        print(f"\n⚠️  发送失败的邮件:")
        for detail in results["details"]:
            if not detail["success"]:
                student = detail["student"]
                print(f"   - {student['chinese_name']} ({student['email']})")

    print(f"\n💡 提示:")
    if send_config["mode"] == "test":
        print(f"   - 测试模式已完成，如需发送全部邮件，请重新运行并选择其他模式")
    elif send_config["mode"] == "batch":
        remaining = len(students) - (send_config["batch_start"] + results["success"])
        if remaining > 0:
            next_start = send_config["batch_start"] + results["success"] + 1
            print(f"   - 剩余 {remaining} 封邮件未发送")
            print(f"   - 下次可从第 {next_start} 封开始发送")
    print(f"   - 建议在非工作时间发送，避免影响正常邮件收发")
    print(f"   - 发送过程中请保持网络连接稳定")


def testAdmissionNotifications():
    """
    测试拟录取通知邮件功能

    功能说明：
    1. 获取所有拟录取学生信息
    2. 依次打印发给他们的邮件内容
    3. 随机挑选一个学生，发送录取邮件到测试邮箱 <EMAIL>

    <AUTHOR>
    @Date 2025-07-20
    """
    import random

    print("🧪 拟录取通知邮件测试")
    print("Author: Kerwin | Date: 2025-07-20")
    print("=" * 60)

    # -------------- 1、加载拟录取学生数据 ----------------------
    excel_file = "【录取15递补5】2025年本科国际生考生录取信息表.xlsx"
    excel_path = Path(__file__).parent.parent / excel_file

    if not excel_path.exists():
        print(f"❌ 错误：找不到Excel文件: {excel_path}")
        return

    print("📊 正在加载拟录取学生数据...")
    admission_students = queryStudentsByType(excel_path, "admission")

    if not admission_students:
        print("ℹ️  没有找到拟录取学生数据")
        return

    print(f"✅ 找到 {len(admission_students)} 名拟录取学生")

    # -------------- 2、依次打印邮件内容 ------------------------
    print(f"\n📧 拟录取学生邮件内容预览：")
    print("=" * 60)

    for i, student in enumerate(admission_students, 1):
        print(f"\n📝 第 {i} 名学生：{student['chinese_name']} ({student['passport_name']})")
        print(f"   专业：{student['major']}")
        print(f"   国籍：{student['nationality']}")
        print(f"   邮箱：{student['email']}")

        # 生成邮件内容
        email_content = generateAdmissionContent(student)
        print(f"   邮件主题：{email_content['subject']}")
        print(f"   邮件正文：")
        # 移除HTML标签以便更好地显示
        import re

        clean_body = re.sub(r"<[^>]+>", "", email_content["body"])
        clean_body = re.sub(r"\s+", " ", clean_body).strip()
        print(f"   {clean_body}")
        print("-" * 40)

    # -------------- 3、随机选择一个学生发送测试邮件 --------------
    print(f"\n🎲 随机选择学生进行测试发送...")

    if len(admission_students) == 0:
        print("❌ 没有学生可供测试")
        return

    # 随机选择一个学生
    test_student = random.choice(admission_students)
    print(f"🎯 选中测试学生：{test_student['chinese_name']} ({test_student['passport_name']})")

    # 询问用户是否确认发送
    print(f"\n📨 准备发送测试邮件到：<EMAIL>")
    print(f"原学生邮箱：{test_student['email']}")

    confirm = input("是否确认发送测试邮件？(y/N): ").strip().lower()
    if confirm != "y":
        print("❌ 用户取消测试邮件发送")
        return

    # -------------- 4、配置邮件发送器并发送测试邮件 --------------
    print(f"\n🚀 开始发送测试邮件...")

    # 邮件配置
    SMTP_SERVER = "smtp.xmut.edu.cn"
    SMTP_PORT = 465
    USERNAME = "<EMAIL>"
    PASSWORD = "2kVWtUnMU3Cagqe9"

    try:
        # 创建邮件发送器
        email_sender = EmailSender(SMTP_SERVER, SMTP_PORT, USERNAME, PASSWORD, sender_name="厦门理工学院招生处")
        print(f"✅ 邮件发送器初始化成功")

        # 生成邮件内容
        email_content = generateAdmissionContent(test_student)

        # 准备附件
        attachment_path = Path(__file__).parent / "厦门理工学院2025学校奖学金招收国际生招生录取信息确认函.docx"
        attachments = None
        if attachment_path.exists():
            attachments = [str(attachment_path)]
            print(f"📎 包含附件: {attachment_path.name}")
        else:
            print(f"⚠️  警告: 确认函附件不存在: {attachment_path}")

        # 发送邮件到测试邮箱
        success = email_sender.sendEmail(
            to_emails=["<EMAIL>"],
            subject=email_content["subject"],
            body=email_content["body"],
            is_html=True,
            attachments=attachments,
        )

        if success:
            print("✅ 测试邮件发送成功！")
            print(f"   收件人：<EMAIL>")
            print(f"   模拟学生：{test_student['chinese_name']} ({test_student['passport_name']})")
            print(f"   邮件主题：{email_content['subject']}")
        else:
            print("❌ 测试邮件发送失败")

    except Exception as e:
        print(f"❌ 邮件发送器初始化或发送失败: {str(e)}")
        return

    print(f"\n📋 测试完成！")
    print(f"💡 提示：请检查测试邮箱 <EMAIL> 查看邮件效果")


if __name__ == "__main__":
    # 检查命令行参数，支持测试模式
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        testAdmissionNotifications()
    else:
        main()
