# 台湾学生录取通知工具

这个工具包含两个主要功能：

1. PDF 拆分器：将包含多个学生信息的 PDF 文件拆分为单独的 PDF 文件
2. 邮件发送器：自动发送台湾学生录取通知邮件

## 功能特点

### PDF 拆分器 (split_pdf.py)

- 自动拆分 PDF 文件的每一页为单独的 PDF 文件
- 智能识别页面中的考生号（支持 `*{ksh}*` 格式）
- 使用考生号作为文件名
- 如果无法识别考生号，则使用页码命名

### 邮件发送器 (send_taiwan_admission_notifications.py)

- 从名单.xlsx 获取学生信息（ksh、xm、邮箱）
- 根据考生号自动匹配 tzs 文件夹中的通知书 PDF
- 自动添加录取通知书和新生入学须知作为附件
- 发送个性化录取通知邮件

## 环境要求

- Python 3.8+
- uv (Python 包管理器)

## 安装和使用

1. 确保已安装 uv：

   ```bash
   # 如果没有安装uv，请先安装
   curl -LsSf https://astral.sh/uv/install.sh | sh
   ```

2. 安装依赖：

   ```bash
   uv sync
   ```

3. 运行脚本：

   **PDF 拆分：**

   ```bash
   uv run python split_pdf.py
   ```

   **发送录取通知邮件：**

   ```bash
   uv run python send_taiwan_admission_notifications.py
   ```

## 文件结构

```
taiwan-student-admission-tool/
├── split_pdf.py                           # PDF拆分脚本
├── send_taiwan_admission_notifications.py # 邮件发送脚本
├── email_sender.py                        # 邮件发送器类
├── pyproject.toml                         # 项目配置文件
├── 名单.xlsx                              # 学生名单Excel文件
├── 厦门理工学院2025级新生入学须知.pdf      # 新生入学须知
├── tzs_ts2025注册.pdf                     # 原始PDF文件
├── tzs/                                   # 拆分后的通知书PDF文件夹
│   ├── 25P511062710001.pdf
│   ├── 25P511062710002.pdf
│   └── ...
└── README.md                             # 说明文档
```

## 脚本说明

### 主要功能

1. **PDF 拆分**: 使用 PyMuPDF 库读取 PDF 文件并拆分每一页
2. **文本提取**: 从每页中提取文本内容
3. **考生号识别**: 使用正则表达式识别考生号
4. **文件命名**: 根据考生号或页码命名输出文件

### 考生号识别规则

脚本会按以下优先级识别考生号：

1. `*{考生号}*` 格式（星号包围的内容）
2. `考生号：{数字}` 格式
3. `准考证号：{数字}` 格式
4. `学号：{数字}` 格式
5. 8 位以上的连续数字

### 自定义配置

如果需要修改输入文件或输出目录，可以编辑 `split_pdf.py` 文件中的以下变量：

```python
input_file = "tzs_ts2025注册.pdf"        # 输入PDF文件名
output_directory = "split_pages"          # 输出目录名
```

## 输出结果

脚本运行后会在 `split_pages` 目录中生成以考生号命名的 PDF 文件。每个文件包含原 PDF 中的一页内容。

## 注意事项

- 确保输入的 PDF 文件在脚本同一目录下
- 输出目录会自动创建
- 如果文件已存在，会被覆盖
- 脚本会显示处理进度和结果统计

## 故障排除

1. **找不到 PDF 文件**: 确保 PDF 文件名正确且在当前目录
2. **依赖安装失败**: 确保已正确安装 uv 和 Python
3. **考生号识别错误**: 检查 PDF 中的文本格式，可能需要调整正则表达式

## 邮件发送功能说明

### 使用前准备

1. **准备 Excel 文件** (`名单.xlsx`)：
   - 必须包含列：`ksh`（考生号）、`xm`（姓名）、`邮箱`
2. **准备 PDF 文件**：

   - 确保 `tzs/` 文件夹中有对应考生号的通知书 PDF 文件
   - 确保根目录有 `厦门理工学院2025级新生入学须知.pdf`

3. **配置邮箱信息**：
   - 编辑 `send_taiwan_admission_notifications.py` 中的 SMTP 配置
   - 修改 `SMTP_SERVER`、`USERNAME`、`PASSWORD` 等参数

### 邮件内容

- **邮件标题**：厦门理工学院 2025 年本科生录取通知书
- **邮件正文**：个性化问候，包含学生姓名
- **附件**：
  - 学生个人录取通知书 PDF（从 tzs 文件夹匹配）
  - 厦门理工学院 2025 级新生入学须知.pdf

### 发送流程

1. 从 Excel 文件读取学生信息
2. 根据考生号匹配对应的通知书 PDF
3. 生成个性化邮件内容
4. 添加附件并发送邮件
5. 显示发送结果统计

### 注意事项

- 脚本会自动跳过没有邮箱或考生号的学生
- 如果找不到对应的通知书 PDF，会跳过该学生并记录错误
- 邮件发送间隔为 3 秒，避免触发频率限制
- 发送前会显示预览信息供确认

## 技术栈

- **PyMuPDF (fitz)**: PDF 处理库
- **pandas**: Excel 文件读取
- **smtplib**: 邮件发送
- **Python re**: 正则表达式处理
- **pathlib**: 文件路径处理
- **uv**: Python 包管理
