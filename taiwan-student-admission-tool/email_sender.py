#!/usr/bin/env python3
"""
SMTP SSL 邮件发送工具
Author: Kerwin
Date: 2025-07-18

功能：使用SMTP SSL协议发送邮件
支持：HTML格式邮件、附件、批量发送
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from email.utils import formataddr
from pathlib import Path
import os
from typing import List, Optional
import logging


class EmailSender:
    """
    SMTP SSL 邮件发送器
    """

    def __init__(self, smtp_server: str, smtp_port: int, username: str, password: str, sender_name: str = None):
        """
        初始化邮件发送器

        @param smtp_server: SMTP服务器地址
        @param smtp_port: SMTP端口号（SSL通常为465）
        @param username: 邮箱用户名
        @param password: 邮箱密码或应用密码
        @param sender_name: 发件人显示名称（可选）
        """
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.sender_name = sender_name

        # 配置日志
        logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
        self.logger = logging.getLogger(__name__)

    def sendEmail(
        self,
        to_emails: List[str],
        subject: str,
        body: str,
        is_html: bool = False,
        cc_emails: Optional[List[str]] = None,
        bcc_emails: Optional[List[str]] = None,
        attachments: Optional[List[str]] = None,
    ) -> bool:
        """
        发送邮件

        @param to_emails: 收件人邮箱列表
        @param subject: 邮件主题
        @param body: 邮件正文
        @param is_html: 是否为HTML格式
        @param cc_emails: 抄送邮箱列表
        @param bcc_emails: 密送邮箱列表
        @param attachments: 附件文件路径列表
        @return: 发送是否成功
        """
        try:
            # -------------- 1、创建邮件对象 ------------------------
            message = MIMEMultipart()

            # 设置发件人（包含名称和邮箱）
            if self.sender_name:
                message["From"] = formataddr((self.sender_name, self.username))
            else:
                message["From"] = self.username

            message["To"] = ", ".join(to_emails)
            message["Subject"] = subject

            if cc_emails:
                message["Cc"] = ", ".join(cc_emails)

            # -------------- 2、添加邮件正文 ------------------------
            if is_html:
                message.attach(MIMEText(body, "html", "utf-8"))
            else:
                message.attach(MIMEText(body, "plain", "utf-8"))

            # -------------- 3、添加附件 ----------------------------
            if attachments:
                self._addAttachments(message, attachments)

            # -------------- 4、建立SSL连接并发送邮件 ----------------
            all_recipients = to_emails.copy()
            if cc_emails:
                all_recipients.extend(cc_emails)
            if bcc_emails:
                all_recipients.extend(bcc_emails)

            context = ssl.create_default_context()

            with smtplib.SMTP_SSL(self.smtp_server, self.smtp_port, context=context) as server:
                server.login(self.username, self.password)
                text = message.as_string()
                server.sendmail(self.username, all_recipients, text)

            self.logger.info(f"邮件发送成功: {subject} -> {to_emails}")
            return True

        except Exception as e:
            self.logger.error(f"邮件发送失败: {str(e)}")
            return False

    def _addAttachments(self, message: MIMEMultipart, attachments: List[str]):
        """
        添加附件到邮件

        @param message: 邮件对象
        @param attachments: 附件文件路径列表
        """
        for file_path in attachments:
            if not Path(file_path).exists():
                self.logger.warning(f"附件文件不存在: {file_path}")
                continue

            try:
                with open(file_path, "rb") as attachment:
                    # 根据文件扩展名设置MIME类型
                    file_ext = Path(file_path).suffix.lower()
                    if file_ext == ".pdf":
                        part = MIMEBase("application", "pdf")
                    elif file_ext in [".jpg", ".jpeg"]:
                        part = MIMEBase("image", "jpeg")
                    elif file_ext == ".png":
                        part = MIMEBase("image", "png")
                    elif file_ext in [".doc", ".docx"]:
                        part = MIMEBase("application", "msword")
                    elif file_ext in [".xls", ".xlsx"]:
                        part = MIMEBase("application", "vnd.ms-excel")
                    else:
                        part = MIMEBase("application", "octet-stream")

                    part.set_payload(attachment.read())

                encoders.encode_base64(part)
                filename = Path(file_path).name

                # 使用RFC2231编码处理中文文件名
                try:
                    # 尝试ASCII编码
                    filename.encode("ascii")
                    # 如果成功，直接使用
                    part.add_header(
                        "Content-Disposition",
                        f'attachment; filename="{filename}"',
                    )
                except UnicodeEncodeError:
                    # 如果包含非ASCII字符，使用RFC2231编码
                    import urllib.parse

                    encoded_filename = urllib.parse.quote(filename, safe="")
                    part.add_header(
                        "Content-Disposition",
                        f"attachment; filename*=UTF-8''{encoded_filename}",
                    )

                message.attach(part)
                self.logger.info(f"已添加附件: {filename}")

            except Exception as e:
                self.logger.error(f"添加附件失败 {file_path}: {str(e)}")
