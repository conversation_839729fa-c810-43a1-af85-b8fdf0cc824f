#!/usr/bin/env python3
"""
PDF页面拆分工具
将PDF文件的每一页拆分为单独的PDF文件，并根据页面中的考生号（ksh）命名
"""

import os
import re
import sys
from pathlib import Path

try:
    import fitz  # PyMuPDF
except ImportError:
    print("请先安装PyMuPDF: uv add pymupdf")
    sys.exit(1)


def extract_ksh_from_text(text):
    """从文本中提取考生号（ksh）"""
    # 查找 *{ksh}* 格式的考生号
    pattern = r"\*([^*]+)\*"
    matches = re.findall(pattern, text)

    if matches:
        # 返回第一个匹配的考生号，去除空格
        return matches[0].strip()

    # 如果没有找到 *{ksh}* 格式，尝试其他可能的格式
    # 查找可能的考生号格式（通常是数字）
    ksh_patterns = [
        r"考生号[：:]\s*(\d+)",
        r"准考证号[：:]\s*(\d+)",
        r"学号[：:]\s*(\d+)",
        r"(\d{8,})",  # 8位以上的数字
    ]

    for pattern in ksh_patterns:
        matches = re.findall(pattern, text)
        if matches:
            return matches[0].strip()

    return None


def split_pdf(input_path, output_dir):
    """拆分PDF文件"""
    input_path = Path(input_path)
    output_dir = Path(output_dir)

    # 确保输出目录存在
    output_dir.mkdir(exist_ok=True)

    # 打开PDF文件
    try:
        doc = fitz.open(input_path)
    except Exception as e:
        print(f"无法打开PDF文件: {e}")
        return

    print(f"PDF文件共有 {len(doc)} 页")

    for page_num in range(len(doc)):
        page = doc[page_num]

        # 提取页面文本
        text = page.get_text()

        # 提取考生号
        ksh = extract_ksh_from_text(text)

        if ksh:
            filename = f"{ksh}.pdf"
        else:
            # 如果没有找到考生号，使用页码命名
            filename = f"page_{page_num + 1:03d}.pdf"
            print(f"第 {page_num + 1} 页未找到考生号，使用默认命名: {filename}")

        # 创建新的PDF文档，只包含当前页
        new_doc = fitz.open()
        new_doc.insert_pdf(doc, from_page=page_num, to_page=page_num)

        # 保存新文件
        output_path = output_dir / filename
        new_doc.save(output_path)
        new_doc.close()

        print(f"已保存: {filename}")

    page_count = len(doc)
    doc.close()
    print(f"拆分完成！共生成 {page_count} 个文件")


def main():
    # 默认输入文件和输出目录
    input_file = "tzs_ts2025注册.pdf"
    output_directory = "split_pages"

    # 检查输入文件是否存在
    if not Path(input_file).exists():
        print(f"错误: 找不到文件 {input_file}")
        print("请确保PDF文件在当前目录中")
        return

    print(f"开始拆分PDF文件: {input_file}")
    print(f"输出目录: {output_directory}")

    split_pdf(input_file, output_directory)


if __name__ == "__main__":
    main()


def main_with_args():
    """支持命令行参数的主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="PDF页面拆分工具")
    parser.add_argument(
        "input_file", nargs="?", default="tzs_ts2025注册.pdf", help="输入PDF文件路径 (默认: tzs_ts2025注册.pdf)"
    )
    parser.add_argument("-o", "--output", default="split_pages", help="输出目录 (默认: split_pages)")

    args = parser.parse_args()

    # 检查输入文件是否存在
    if not Path(args.input_file).exists():
        print(f"错误: 找不到文件 {args.input_file}")
        return

    print(f"开始拆分PDF文件: {args.input_file}")
    print(f"输出目录: {args.output}")

    split_pdf(args.input_file, args.output)


if __name__ == "__main__":
    # 如果有命令行参数，使用参数版本，否则使用默认版本
    if len(sys.argv) > 1:
        main_with_args()
    else:
        main()
